# Scoop Xifan Bucket

[![Tests](https://github.com/xifan2333/scoop-xifan/actions/workflows/ci.yml/badge.svg)](https://github.com/xifan2333/scoop-xifan/actions/workflows/ci.yml)
[![Excavator](https://github.com/xifan2333/scoop-xifan/actions/workflows/excavator.yml/badge.svg)](https://github.com/xifan2333/scoop-xifan/actions/workflows/excavator.yml)

个人维护的 Scoop bucket，包含一些常用软件的安装配置。

## 安装方式

```powershell
# 添加 bucket
scoop bucket add xifan https://github.com/xifan2333/scoop-xifan

# 安装软件
scoop install xifan/<软件名>
```

## 软件列表

> 📊 当前收录软件：**1** 个  
> 🗂️ 分类数量：**1** 个  
> 🕐 最后更新：2025-08-05 07:27:46


### 📁 工具

| 软件名 | 版本 | 描述 | 许可证 |
|--------|------|------|--------|
| `uninstall-tool` | 3.7.3.5719 | [Geek 卸载工具专业版](https://pan.xifan.fun/scoop/) | Freeware |

---

## 贡献指南

欢迎提交 Issue 和 Pull Request！

### 添加新软件

1. 在 `bucket/` 目录下创建新的 JSON manifest 文件
2. 确保 `description` 字段使用 `分类 | 软件名称` 的格式
3. 运行 `.\bin\generate-readme.ps1` 更新 README
4. 提交 Pull Request

### 本地测试

```powershell
# 检查 URLs
.\bin\checkurls.ps1

# 检查 hashes
.\bin\checkhashes.ps1

# 检查版本更新
.\bin\checkver.ps1
```

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。
